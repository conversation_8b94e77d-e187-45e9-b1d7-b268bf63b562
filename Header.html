<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />

    <title>Summer Brand</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'peach': '#E8BE9A',      // Peach Beige - Primary background
              'sage': '#A7B9A3',       // Muted Sage Green - Accent color
              'sun': '#F7D488',        // Sun Yellow - Call-to-action highlights
              'coral': '#EB9362',      // Coral Orange - Button hovers, headings
              'teal': '#7ABFC3',       // Soft Teal Blue - Secondary accent
              'charcoal': '#2C3A2F',   // Charcoal Grey - Text & logo contrast
              'offwhite': '#FAF8F4',   // Off White - Light backgrounds
            }
          }
        }
      }
    </script>
    <style>
      body, html {
        margin: 0;
        padding: 0;
        width: 100%;
        overflow-x: hidden;
      }
      .full-width-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        position: relative;
      }
    </style>
  </head>
  <body class="m-0 p-0 overflow-x-hidden">
    <div class="flex flex-col min-h-screen bg-offwhite" style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'>
      <header class="flex items-center justify-between whitespace-nowrap px-10 py-3 bg-peach/30 z-10">
        <div class="flex items-center gap-4 text-charcoal">
          <div class="size-4">
            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z" fill="currentColor"></path>
            </svg>
          </div>
          <h2 class="text-charcoal text-lg font-bold leading-tight tracking-[-0.015em]">Summer Brand</h2>
        </div>
        <div class="flex flex-1 justify-end gap-8">
          <div class="flex items-center gap-9">
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Home</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Lookbook</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Shop Collection</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Stories</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">About</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Contact</a>
          </div>
          <button
            class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-sage hover:bg-coral text-charcoal gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5 transition-colors"
          >
            <div class="text-charcoal" data-icon="MagnifyingGlass" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
          </button>
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-sage"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAIctFimAXJ-3qX9ASNzx-QApQ1UISFG-tUDVirOc149pnVNLm_OA3R2UHnWo3me4n0cOodHPE6lLFxqQmNddsUxfpcnvCifIvAp-YQiynulV8Gj5g6BhjbX2-7E24yD_SPVbKk0jNOyPQEovP_Bv1U0EDY2Ai46jnQj5KWOP2I058a1hJKylpbPtJY5kIaHHKZUHEwgm6lGb1R1LWemuwT3vNTncflhhQH6-STNVPeiuNNsCFTjblVGGq0iW4oKEB-tCV2Y-7IakSw");'
          ></div>
        </div>
      </header>
      
      <div class="flex min-h-[calc(100vh-80px)] w-screen flex-col gap-6 bg-cover bg-center bg-no-repeat items-center justify-center border-0 pt-20"
           style='background-image: linear-gradient(rgba(44, 58, 47, 0.1) 0%, rgba(44, 58, 47, 0.4) 100%), url("https://i.imgur.com/bIjiAo5.jpeg"); margin-left: calc(-50vw + 50%); width: 100vw;'>
        <!-- Hero content here -->
      </div>
      
      <div class="flex-1 bg-offwhite">
        <!-- Rest of content would go here -->
      </div>
    </div>
  </body>
</html>
