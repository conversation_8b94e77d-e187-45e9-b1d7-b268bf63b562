<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />

    <title>Summer Brand</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'peach': '#E8BE9A',      // Peach Beige - Primary background
              'sage': '#A7B9A3',       // Muted Sage Green - Accent color
              'sun': '#F7D488',        // Sun Yellow - Call-to-action highlights
              'coral': '#EB9362',      // Coral Orange - Button hovers, headings
              'teal': '#7ABFC3',       // Soft Teal Blue - Secondary accent
              'charcoal': '#2C3A2F',   // Charcoal Grey - Text & logo contrast
              'offwhite': '#FAF8F4',   // Off White - Light backgrounds
            }
          }
        }
      }
    </script>
    <style>
      body, html {
        margin: 0;
        padding: 0;
        width: 100%;
        overflow-x: hidden;
      }
      .full-width-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        position: relative;
      }
      .mobile-menu {
        display: none;
      }
      .mobile-menu.active {
        display: block;
      }
      @media (max-width: 1024px) {
        .desktop-nav {
          display: none !important;
        }
        .mobile-nav {
          display: flex !important;
        }
      }
      @media (min-width: 1025px) {
        .mobile-nav {
          display: none !important;
        }
        .desktop-nav {
          display: flex !important;
        }
      }
    </style>
  </head>
  <body class="m-0 p-0 overflow-x-hidden">
    <div class="flex flex-col min-h-screen bg-offwhite" style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'>
      <header class="flex items-center justify-between whitespace-nowrap px-4 md:px-10 py-3 bg-peach/30 z-10">
        <div class="flex items-center gap-2 md:gap-4 text-charcoal">
          <img src="logo.png" alt="Summer Brand Logo" class="h-6 md:h-8 w-auto">
          <h2 class="text-charcoal text-base md:text-lg font-bold leading-tight tracking-[-0.015em]">Summer Brand</h2>
        </div>

        <!-- Desktop Navigation -->
        <div class="desktop-nav flex flex-1 justify-end gap-8">
          <div class="flex items-center gap-9">
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Home</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Lookbook</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Shop Collection</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Stories</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">About</a>
            <a class="text-charcoal hover:text-coral text-sm font-medium leading-normal transition-colors" href="#">Contact</a>
          </div>
          <button
            class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-sage hover:bg-coral text-charcoal gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5 transition-colors"
          >
            <div class="text-charcoal" data-icon="MagnifyingGlass" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
          </button>
          <img src="user_icon.png" alt="User Profile" class="rounded-full size-10 border-2 border-sage object-cover">
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-nav hidden items-center gap-2">
          <button
            class="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 bg-sage hover:bg-coral text-charcoal gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2 transition-colors"
          >
            <div class="text-charcoal" data-icon="MagnifyingGlass" data-size="16px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
          </button>
          <img src="user_icon.png" alt="User Profile" class="rounded-full size-8 border-2 border-sage object-cover">
          <button class="text-charcoal p-2" onclick="toggleMobileMenu()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
            </svg>
          </button>
        </div>
      </header>

      <!-- Mobile Menu -->
      <div id="mobileMenu" class="mobile-menu bg-peach/95 px-4 py-4">
        <div class="flex flex-col gap-4">
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">Home</a>
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">Lookbook</a>
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">Shop Collection</a>
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">Stories</a>
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">About</a>
          <a class="text-charcoal hover:text-coral text-base font-medium leading-normal transition-colors py-2" href="#">Contact</a>
        </div>
      </div>
      
      <div class="flex min-h-[calc(100vh-80px)] w-screen flex-col gap-6 bg-cover bg-center bg-no-repeat items-center justify-center border-0"
           style='background-image: url("https://i.imgur.com/bIjiAo5.jpeg"); margin-left: calc(-50vw + 50%); width: 100vw;'>
        <!-- Hero content here -->
      </div>
      
      <div class="flex-1 bg-offwhite">
        <!-- Rest of content would go here -->
      </div>
    </div>

    <script>
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobileMenu');
        mobileMenu.classList.toggle('active');
      }
    </script>
  </body>
</html>
